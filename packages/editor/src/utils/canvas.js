import { decode, encode } from "@thi.ng/rle-pack";
import chroma from "chroma-js";
import Constants from "../core/Constants";

import * as Colors from "./colors";

// given the imageData object returns the DOM Image with loaded data
function imageData2Image(imagedata) {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = imagedata.width;
  canvas.height = imagedata.height;
  ctx.putImageData(imagedata, 0, 0);

  const image = new Image();

  image.src = canvas.toDataURL();
  return image;
}

// given the RLE array returns the DOM Image element with loaded image
function RLE2Region(rle, image, { color = Constants.FILL_COLOR } = {}) {
  const nw = image.naturalWidth,
    nh = image.naturalHeight;

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = nw;
  canvas.height = nh;

  const newdata = ctx.createImageData(nw, nh);

  let decoded_rle = decode(rle);
  // Create an empty array with length = 4 times the length of decoded rle
  // log length of decoded rle divided by nh (height of image) and nw
  let masks_data;
  if (decoded_rle.length / (nh * nw) == 1) {
    masks_data = new Uint8ClampedArray(decoded_rle.length * 4);
    for (let i = 0; i < decoded_rle.length; i++) {
      if (decoded_rle[i] > 0.0) {
        masks_data[4 * i + 0] = 255;
        masks_data[4 * i + 1] = 255;
        masks_data[4 * i + 2] = 255;
        masks_data[4 * i + 3] = 255;
      }
    }
  } else {
    masks_data = decoded_rle;
  }

  // newdata.data.set(decode(rle));
  newdata.data.set(masks_data);
  const rgb = chroma(color).rgb();

  for (let i = newdata.data.length / 4; i--; ) {
    if (newdata.data[i * 4 + 3]) {
      newdata.data[i * 4] = rgb[0];
      newdata.data[i * 4 + 1] = rgb[1];
      newdata.data[i * 4 + 2] = rgb[2];
    }
  }
  ctx.putImageData(newdata, 0, 0);

  const new_image = new Image();

  new_image.src = canvas.toDataURL();
  return new_image;
}

// given the Mask returns the DOM Image element with loaded image
function Mask2Region(mask, image, { color = Constants.FILL_COLOR } = {}) {
  const nw = image.naturalWidth,
    nh = image.naturalHeight;

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = nw;
  canvas.height = nh;

  const newdata = ctx.createImageData(nw, nh);

  const rgb = chroma(color).rgb();

  for (let i = mask.length; i--; ) {
    if (newdata.data[i * 4 + 3]) {
      newdata.data[i * 4] = rgb[0];
      newdata.data[i * 4 + 1] = rgb[1];
      newdata.data[i * 4 + 2] = rgb[2];
    }
  }
  ctx.putImageData(newdata, 0, 0);

  const new_image = new Image();

  new_image.src = canvas.toDataURL();
  return new_image;
}

// Hàm gốc được chuyển thành hàm helper
function _Region2RLE(region, image, onechannelformat = true) {
  const nw = image.naturalWidth,
    nh = image.naturalHeight;
  const stage = region.object?.stageRef;
  const parent = region.parent;

  if (!stage) {
    console.error(`Stage not found for area #${region.cleanId}`);
    return;
  }

  const layer = stage.findOne(`#${region.cleanId}`);
  if (!layer) {
    console.error(`Layer #${region.id} was not found on Stage`);
    return [];
  }

  // Lưu trạng thái hiện tại
  const originalState = {
    width: stage.getWidth(),
    height: stage.getHeight(),
    scaleX: stage.getScaleX(),
    scaleY: stage.getScaleY(),
    x: stage.getX(),
    y: stage.getY(),
    offsetX: stage.getOffsetX(),
    offsetY: stage.getOffsetY(),
    rotation: stage.getRotation(),
    isVisible: layer.visible()
  };

  // Tạm thời thay đổi stage để render
  !originalState.isVisible && layer.show();
  const highlight = layer.findOne(".highlight");
  highlight?.hide();

  // Thiết lập stage cho việc render
  stage
    .setWidth(parent.stageWidth)
    .setHeight(parent.stageHeight)
    .setScaleX(1)
    .setScaleY(1)
    .setX(0)
    .setY(0)
    .setOffsetX(0)
    .setOffsetY(0)
    .setRotation(0);
  stage.drawScene();

  // Lấy canvas data
  const canvas = layer.toCanvas({ pixelRatio: nw / image.stageWidth });
  const ctx = canvas.getContext("2d", { willReadFrequently: true });
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

  // Khôi phục trạng thái
  highlight?.show();
  stage
    .setWidth(originalState.width)
    .setHeight(originalState.height)
    .setScaleX(originalState.scaleX)
    .setScaleY(originalState.scaleY)
    .setX(originalState.x)
    .setY(originalState.y)
    .setOffsetX(originalState.offsetX)
    .setOffsetY(originalState.offsetY)
    .setRotation(originalState.rotation);
  stage.drawScene();
  !originalState.isVisible && layer.hide();

  return { imageData, onechannelformat };
}

// Hàm xử lý RLE trong một Promise để không chặn UI thread
function processRLE({ imageData, onechannelformat }) {
  return new Promise(resolve => {
    // Sử dụng setTimeout để trả quyền điều khiển lại cho UI thread
    setTimeout(() => {
      let rle;
      if (onechannelformat) {
        const length = imageData.data.length / 4;
        const filtered = new Uint8Array(length);
        for (let i = 0, j = 0; i < imageData.data.length; i += 4, j++) {
          filtered[j] = imageData.data[i];
        }
        rle = encode(filtered, length);
      } else {
        rle = encode(imageData.data, imageData.data.length);
      }
      resolve(rle);
    }, 0);
  });
}

// Hàm chính được export - trả về Promise
function Region2RLE(region, image, onechannelformat = true) {
  // Hiển thị loading nếu cần
  if (region.setIsLoading) {
    region.setIsLoading(true);
  }

  return new Promise((resolve, reject) => {
    try {
      const data = _Region2RLE(region, image, onechannelformat);
      if (!data) {
        if (region.setIsLoading) region.setIsLoading(false);
        resolve([]);
        return;
      }

      processRLE(data).then(result => {
        if (region.setIsLoading) region.setIsLoading(false);
        resolve(result);
      });
    } catch (error) {
      console.error("Error in Region2RLE:", error);
      if (region.setIsLoading) region.setIsLoading(false);
      reject(error);
    }
  });
}

function brushSizeCircle(size) {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = size * 4 + 8;
  canvas.height = size * 4 + 8;

  ctx.beginPath();
  ctx.arc(size / 2 + 4, size / 2 + 4, size / 2, 0, 2 * Math.PI, false);

  ctx.lineWidth = 2;
  ctx.strokeStyle = "white";
  ctx.stroke();

  return canvas.toDataURL();
}

function encodeSVG(data) {
  const externalQuotesValue = "single";

  function getQuotes() {
    const double = `"`;
    const single = `'`;

    return {
      level1: externalQuotesValue === "double" ? double : single,
      level2: externalQuotesValue === "double" ? single : double
    };
  }

  const quotes = getQuotes();

  function addNameSpace(data) {
    if (data.indexOf("http://www.w3.org/2000/svg") < 0) {
      data = data.replace(
        /<svg/g,
        `<svg xmlns=${quotes.level2}http://www.w3.org/2000/svg${quotes.level2}`
      );
    }

    return data;
  }

  data = addNameSpace(data);
  const symbols = /[\r\n%#()<>?[\\\]^`{|}]/g;

  // Use single quotes instead of double to avoid encoding.
  if (externalQuotesValue === "double") {
    data = data.replace(/"/g, "'");
  } else {
    data = data.replace(/'/g, '"');
  }

  data = data.replace(/>\s{1,}</g, "><");
  data = data.replace(/\s{2,}/g, " ");

  // var resultCss = `background-image: url();`;

  const escaped = data.replace(symbols, encodeURIComponent);

  return `${quotes.level1}data:image/svg+xml,${escaped}${quotes.level1}`;
}

const labelToSVG = (function() {
  const SVG_CACHE = {};

  function calculateTextWidth(text) {
    const svg = document.createElement("svg");
    const svgText = document.createElement("text");

    svgText.style =
      "font-size: 9.5px; font-weight: bold; color: red; fill: red; font-family: Monaco";
    svgText.innerHTML = text;

    svg.appendChild(svgText);
    document.body.appendChild(svg);

    const textLen = svgText.getBoundingClientRect().width;

    svg.remove();

    return textLen;
  }

  return function({ label, score }) {
    let cacheKey = label;

    if (score !== null) cacheKey = cacheKey + score;

    if (cacheKey in SVG_CACHE) return SVG_CACHE[cacheKey];

    let width = 0;
    const items = [];

    if (score !== null && score !== undefined) {
      const fillColor = Colors.getScaleGradient(score);

      items.push(
        `<rect x="0" y="0" rx="2" ry="2" width="24" height="14" style="fill:${fillColor};opacity:0.5" />`
      );
      items.push(
        `<text x="3" y="10" style="font-size: 8px; font-family: Monaco">${score.toFixed(
          2
        )}</text>`
      );
      width = width + 26;
    }

    if (label) {
      items.push(
        `<text x="${width}" y="11" style="font-size: 9.5px; font-weight: bold; font-family: Monaco">${label}</text>`
      );
      width = width + calculateTextWidth(label) + 2;
    }

    const res = `<svg height="16" width="${width}">${items.join("")}</svg>`;
    const enc = encodeSVG(res);

    SVG_CACHE[cacheKey] = enc;
    return enc;
  };
})();

/**
 *
 * @param {HTMLCanvasElement} canvas
 * @returns {{
 * canvas: HTMLCanvasElement,
 * bbox: {
 *   left: number,
 *   top: number,
 *   right: number,
 *   bottom: number,
 *   width: number,
 *   height: number
 * }
 * }}
 */
const trim = canvas => {
  let copy,
    width = canvas.width,
    height = canvas.height;
  const ctx = canvas.getContext("2d");
  const bbox = {
    top: null,
    left: null,
    right: null,
    bottom: null
  };

  try {
    copy = document.createElement("canvas").getContext("2d");
    const pixels = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const l = pixels.data.length;
    let i, x, y;

    for (i = 0; i < l; i += 4) {
      if (pixels.data[i + 3] !== 0) {
        x = (i / 4) % canvas.width;
        y = ~~(i / 4 / canvas.width);

        if (bbox.top === null) {
          bbox.top = y;
        }

        if (bbox.left === null) {
          bbox.left = x;
        } else if (x < bbox.left) {
          bbox.left = x;
        }

        if (bbox.right === null) {
          bbox.right = x;
        } else if (bbox.right < x) {
          bbox.right = x;
        }

        if (bbox.bottom === null) {
          bbox.bottom = y;
        } else if (bbox.bottom < y) {
          bbox.bottom = y;
        }
      }
    }

    width = bbox.right - bbox.left;
    height = bbox.bottom - bbox.top;
    const trimmed = ctx.getImageData(bbox.left, bbox.top, width, height);

    copy.canvas.width = width;
    copy.canvas.height = height;
    copy.putImageData(trimmed, 0, 0);
  } catch (err) {
    /* Gotcha! */
  }

  // open new window with trimmed image:
  return {
    canvas: copy?.canvas ?? canvas,
    bbox: {
      ...bbox,
      width,
      height
    }
  };
};

export default {
  imageData2Image,
  Region2RLE,
  RLE2Region,
  Mask2Region,
  brushSizeCircle,
  labelToSVG,
  trim
};
